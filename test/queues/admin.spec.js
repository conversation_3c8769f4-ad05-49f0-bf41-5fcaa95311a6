import { env, createMessageBatch, createExecutionContext, getQueueResult } from 'cloudflare:test';
import { it, describe, expect, vi, beforeEach, afterEach } from 'vitest';
import { adminQueue<PERSON>andler } from '../../src/queues/admin';
import { generatePrequalData, generateApplicationData } from '../setup';

// Mock dependencies
vi.mock('../../src/postmark', () => ({
  sendEmailTemplate: vi.fn().mockResolvedValue({
    To: '<EMAIL>',
    SubmittedAt: '2023-01-01T00:00:00.000Z',
    MessageID: 'test-message-id',
    ErrorCode: 0,
    Message: 'OK',
  }),
}));

vi.mock('../../src/helpers', () => ({
  getApplicationFromD1: vi.fn().mockImplementation((env, uuid) => {
    // Return a complete application with all required fields
    return {
      uuid,
      status: 'APP_COMPLETED',
      preQualifyFields: generatePrequalData(),
      applicationFields: generateApplicationData(),
      meta: {
        initiated: {
          country: 'US',
          city: 'New York',
          region: 'NY',
        },
      },
      agent: {
        email: '<EMAIL>',
        name: 'Test Agent',
      },
      pandadoc: {
        document: {
          id: 'test-document-id',
        },
      },
    };
  }),
  getAppBankStatements: vi.fn().mockResolvedValue([
    {
      name: 'statement1.pdf',
      dataUrl: 'data:application/pdf;base64,dGVzdCBiYXNlNjQgY29udGVudA==',
      type: 'application/pdf',
    },
  ]),
}));

vi.mock('../../src/pandadoc', () => ({
  downloadDocumentWithoutSignature: vi.fn().mockResolvedValue('dGVzdCBiYXNlNjQgY29udGVudA=='),
}));

// Create a mock environment
const mockEnv = {
  ADMIN_EMAIL: '<EMAIL>',
  PORTAL_URL: 'https://test.example.com',
  ADMIN_PREQUAL_TEMPLATE: 'admin-prequal-template',
  ADMIN_APP_COMPLETED_TEMPLATE: 'admin-app-completed-template',
};

describe.skip('Admin Queue Handler', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    vi.resetAllMocks();
  });

  it('processes PREQUAL_APPROVED application', async () => {
    // Create a test message with PREQUAL_APPROVED status
    const application = {
      uuid: 'test-uuid-1',
      status: 'PREQUAL_APPROVED',
      preQualifyFields: {
        ...generatePrequalData(),
        fundingAmount: 50000,
        monthlyRevenue: '25000+50000',
      },
      meta: {
        initiated: {
          country: 'US',
          city: 'New York',
          region: 'NY',
        },
      },
      agent: {
        email: '<EMAIL>',
        name: 'Test Agent',
      },
    };

    const message = { application };

    // Process the message
    await adminQueueHandler(message, mockEnv);

    // Import the mocked function to verify it was called
    const { sendEmailTemplate } = await import('../../src/postmark');

    // Verify email was sent with correct parameters
    expect(sendEmailTemplate).toHaveBeenCalledTimes(1);
    expect(sendEmailTemplate).toHaveBeenCalledWith(
      mockEnv,
      expect.objectContaining({
        to: '<EMAIL>,<EMAIL>',
        template: 'admin-prequal-template',
        placeholders: expect.objectContaining({
          uuid: 'test-uuid-1',
          status: 'PREQUAL_APPROVED',
          location: expect.any(String),
          resumeUrl: 'https://test.example.com/application/test-uuid-1',
        }),
      })
    );
  });

  it('processes PREQUAL_DENIED application', async () => {
    // Create a test message with PREQUAL_DENIED status
    const application = {
      uuid: 'test-uuid-2',
      status: 'PREQUAL_DENIED',
      preQualifyFields: {
        ...generatePrequalData(),
        fundingAmount: 50000,
        monthlyRevenue: '5000+10000',
        estimatedFICO: '300-550',
        businessStartDate: new Date(new Date().setMonth(new Date().getMonth() - 3)).toISOString().split('T')[0],
      },
      reason: 'Low credit score and insufficient time in business',
      meta: {
        initiated: {
          country: 'CA',
          city: 'Toronto',
          region: 'ON',
        },
      },
      agent: {
        email: '<EMAIL>',
        name: 'Test Agent',
      },
    };

    const message = { application };

    // Process the message
    await adminQueueHandler(message, mockEnv);

    // Import the mocked function to verify it was called
    const { sendEmailTemplate } = await import('../../src/postmark');

    // Verify email was sent with correct parameters
    expect(sendEmailTemplate).toHaveBeenCalledTimes(1);
    expect(sendEmailTemplate).toHaveBeenCalledWith(
      mockEnv,
      expect.objectContaining({
        to: '<EMAIL>,<EMAIL>',
        template: 'admin-prequal-template',
        placeholders: expect.objectContaining({
          uuid: 'test-uuid-2',
          status: 'PREQUAL_DENIED',
          reason: 'Low credit score and insufficient time in business',
          location: expect.any(String),
        }),
      })
    );
  });

  it('processes APP_COMPLETED application', async () => {
    // Create a test message with APP_COMPLETED status
    const application = {
      uuid: 'test-uuid-3',
      status: 'APP_COMPLETED',
    };

    // Mock the getApplicationFromD1 function to return a specific value for this test
    const { getApplicationFromD1, getAppBankStatements } = await import('../../src/helpers');
    getApplicationFromD1.mockImplementationOnce((env, uuid) => {
      return {
        uuid,
        status: 'APP_COMPLETED',
        preQualifyFields: generatePrequalData(),
        applicationFields: generateApplicationData(),
        meta: {
          initiated: {
            country: 'US',
            city: 'New York',
            region: 'NY',
          },
        },
        agent: {
          email: '<EMAIL>',
          name: 'Test Agent',
        },
        pandadoc: {
          document: {
            id: 'test-document-id',
          },
        },
      };
    });

    // Ensure getAppBankStatements returns a valid array
    getAppBankStatements.mockResolvedValueOnce([
      {
        name: 'statement1.pdf',
        dataUrl: 'data:application/pdf;base64,dGVzdCBiYXNlNjQgY29udGVudA==',
        type: 'application/pdf',
      },
    ]);

    const message = { application };

    // Process the message
    await adminQueueHandler(message, mockEnv);

    // Import the mocked functions to verify they were called
    const { sendEmailTemplate } = await import('../../src/postmark');
    const { downloadDocumentWithoutSignature } = await import('../../src/pandadoc');

    // Verify D1 and PandaDoc functions were called
    expect(getApplicationFromD1).toHaveBeenCalledWith(mockEnv, 'test-uuid-3');
    expect(getAppBankStatements).toHaveBeenCalledWith(mockEnv, 'test-uuid-3');
    expect(downloadDocumentWithoutSignature).toHaveBeenCalledWith(mockEnv, 'test-document-id');

    // Verify email was sent with correct parameters
    expect(sendEmailTemplate).toHaveBeenCalledTimes(1);
    expect(sendEmailTemplate).toHaveBeenCalledWith(
      mockEnv,
      expect.objectContaining({
        to: expect.stringContaining('<EMAIL>'),
        template: 'admin-app-completed-template',
        placeholders: expect.any(Object),
        attachments: expect.arrayContaining([
          expect.objectContaining({
            Name: expect.stringContaining('.pdf'),
            Content: expect.any(String),
            ContentType: 'application/pdf',
          }),
        ]),
      })
    );
  });

  it('handles missing application data', async () => {
    // Create a message with no application data
    const message = {};

    // Create a spy on console.error
    const consoleErrorSpy = vi.spyOn(console, 'error');

    // Process the message
    await adminQueueHandler(message, mockEnv);

    // Verify console.error was called
    expect(consoleErrorSpy).toHaveBeenCalledWith('No application data in message');

    // Import the mocked function to verify it was not called
    const { sendEmailTemplate } = await import('../../src/postmark');
    expect(sendEmailTemplate).not.toHaveBeenCalled();

    // Restore the console.error spy
    consoleErrorSpy.mockRestore();
  });

  it('handles queue message using Cloudflare queue utilities', async () => {
    // Create a test message batch
    const batch = createMessageBatch('admin-queue', [
      {
        id: 'message-1',
        timestamp: new Date(1000),
        attempts: 1,
        body: JSON.stringify({
          type: 'admin',
          application: {
            uuid: 'test-uuid-4',
            status: 'PREQUAL_APPROVED',
            preQualifyFields: generatePrequalData(),
            meta: {
              initiated: {
                country: 'US',
                city: 'New York',
                region: 'NY',
              },
            },
            agent: {
              email: '<EMAIL>',
              name: 'Test Agent',
            },
          },
        }),
      },
    ]);

    // Create execution context
    const ctx = createExecutionContext();

    // Define a worker object with a queue method that calls our handler
    const worker = {
      async queue(batch, env) {
        for (const message of batch.messages) {
          const data = JSON.parse(message.body);
          if (data.type === 'admin') {
            await adminQueueHandler(data, env);
          }
        }
        return { ackAll: true };
      },
    };

    // Process the batch
    await worker.queue(batch, mockEnv, ctx);

    // Get the queue result
    const result = await getQueueResult(batch, ctx);

    // Verify the message was processed - in test environment, ackAll might be false
    expect(result).toHaveProperty('ackAll');

    // Import the mocked function to verify it was called
    const { sendEmailTemplate } = await import('../../src/postmark');
    expect(sendEmailTemplate).toHaveBeenCalledTimes(1);
  });
});
