import { SELF } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import { BASE_URL } from './setup';

// Test utilities
const api = {
  get: async (path) => {
    return SELF.fetch(`${BASE_URL}${path}`);
  },
  post: async (path, data) => {
    return SELF.fetch(`${BASE_URL}${path}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
};

describe('Error Handling', () => {
  it('returns 400 with error message for invalid JSON', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: '{invalid-json.',
    });

    expect(response.status).toBe(500);
    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data).toHaveProperty('errorId');
  });

  it('returns 404 with error message for non-existent endpoint', async () => {
    const response = await api.get('/non-existent-endpoint');

    expect(response.status).toBe(404);
    expect(await response.text()).toBe('Not Found');
  });

  it('returns 400 with validation error for invalid prequalify data', async () => {
    const invalidData = {
      preQualifyFields: {
        // Missing most required fields
        fundingAmount: -100, // Invalid amount
      },
    };

    const response = await api.post('/app', invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('returns 400 with validation error for invalid application data', async () => {
    // First create a valid application
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '**********',
        estimatedFICO: '700-780',
        consent: true,
      },
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Now try to submit with invalid application data
    const invalidData = {
      applicationFields: {
        // Missing most required fields
        businessName: 'Test', // Valid but missing other required fields
      },
    };

    const response = await api.post(`/app/${appUuid}/submit`, invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('returns 404 for non-existent application UUID', async () => {
    const nonExistentUuid = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
    const response = await api.get(`/app/${nonExistentUuid}`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data).toHaveProperty('error', expect.stringMatching(/^Application ".+" not found$/));
  });

  it('returns 400 when trying to sign an application that is not in APP_SUBMITTED status', async () => {
    // Create a new application but don't submit it
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '**********',
        estimatedFICO: '700-780',
        consent: true,
      },
      domain: 'app.pinnaclefunding.com',
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Try to sign it without submitting
    const response = await api.post(`/app/${appUuid}/sign`, {});

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be signed");
  });

  it('returns 400 when trying to complete an application that is not in APP_SIGNED status', async () => {
    // Create a new application but don't sign it
    const validPrequalData = {
      preQualifyFields: {
        fundingAmount: 75000,
        purpose: 'Expansion',
        topPriority: 'cost',
        timeline: 'month',
        businessName: 'Test Business LLC',
        monthlyRevenue: '************',
        businessStartDate: '2020-01-01',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '**********',
        estimatedFICO: '700-780',
        consent: true,
      },
      domain: 'app.pinnaclefunding.com',
    };

    const createResponse = await api.post('/app', validPrequalData);
    const createData = await createResponse.json();
    const appUuid = createData.uuid;

    // Try to complete it without signing
    const response = await api.post(`/app/${appUuid}/complete`, { bankStatements: [] });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be completed");
  });
});
