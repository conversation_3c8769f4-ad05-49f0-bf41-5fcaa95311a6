import { SELF } from 'cloudflare:test';
import { beforeAll, describe, expect, it } from 'vitest';
import { BASE_URL, generateApplicationData, generateCreateAppRequest, generateDeniedPrequalData } from './setup';

// Test utilities
const api = {
  get: async (path) => {
    return SELF.fetch(`${BASE_URL}${path}`);
  },
  post: async (path, data) => {
    return SELF.fetch(`${BASE_URL}${path}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
};


// Basic API Tests
describe('API Endpoints', () => {
  it('responds with OK', async () => {
    const response = await api.get('/');
    expect(response.status).toBe(200);
    expect(await response.text()).toMatchInlineSnapshot(`"OK"`);
  });

  it("responds with 'Not Found' for unknown routes", async () => {
    const response = await api.get('/unknown');
    expect(response.status).toBe(404);
    expect(await response.text()).toMatchInlineSnapshot(`"Not Found"`);
  });

  it('rejects POST /app requests larger than 1MB', async () => {
    // Create a payload slightly larger than 1MB
    const largePayload = {
      data: 'x'.repeat(1024 * 1024 + 100), // 1MB + 100 bytes
    };

    const response = await SELF.fetch(`${BASE_URL}/app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(largePayload),
    });

    expect(response.status).toBe(413);
  });
});

// Application Creation Tests
describe('Application Creation', () => {
  it('creates a new application with valid prequalify data', async () => {
    const requestData = generateCreateAppRequest();
    const response = await api.post('/app', requestData);

    expect(response.status).toBe(201);

    const data = await response.json();
    expect(data).toHaveProperty('uuid');
    expect(data).toHaveProperty('status', 'PREQUAL_APPROVED');
    expect(data).toHaveProperty('agent');
  });

  it('denies application with invalid prequalify data', async () => {
    const requestData = generateCreateAppRequest({ preQualifyFields: generateDeniedPrequalData() });
    const response = await api.post('/app', requestData);

    expect(response.status).toBe(201); // Still creates the application but with PREQUAL_DENIED status

    const data = await response.json();
    expect(data).toHaveProperty('uuid');
    expect(data).toHaveProperty('status', 'PREQUAL_DENIED');
    expect(data).toHaveProperty('reason');
  });

  it('rejects invalid prequalify data format', async () => {
    const invalidData = {
      preQualifyFields: {
        // Missing required fields
        fundingAmount: 75000,
      },
      domain: 'app.pinnaclefunding.com',
    };

    const response = await api.post('/app', invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('rejects invalid domain', async () => {
    const invalidData = generateCreateAppRequest({ domain: 'invalid.domain.com' });

    const response = await api.post('/app', invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });

  it('accepts valid domain', async () => {
    const requestData = generateCreateAppRequest({ domain: 'ft.pinnaclefunding.com' });
    const response = await api.post('/app', requestData);

    expect(response.status).toBe(201);

    const data = await response.json();
    expect(data).toHaveProperty('uuid');
    expect(data).toHaveProperty('status', 'PREQUAL_APPROVED');
  });
});

// Application Retrieval Tests
describe('Application Retrieval', () => {
  let appUuid;

  beforeAll(async () => {
    // Create a test application to retrieve
    const requestData = generateCreateAppRequest();
    const response = await api.post('/app', requestData);
    const data = await response.json();
    appUuid = data.uuid;
  });

  it('retrieves an existing application by UUID', async () => {
    const response = await api.get(`/app/${appUuid}`);

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'PREQUAL_APPROVED');
    expect(data.data).toHaveProperty('preQualifyFields');
  });

  it('returns 404 for non-existent application UUID', async () => {
    const nonExistentUuid = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
    const response = await api.get(`/app/${nonExistentUuid}`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data).toHaveProperty('error', expect.stringMatching(/^Application ".+" not found$/));
  });
});

// Application Submission Tests
describe('Application Submission', () => {
  let appUuid;

  beforeAll(async () => {
    // Create a test application to submit
    const requestData = generateCreateAppRequest();
    const response = await api.post('/app', requestData);
    const data = await response.json();
    appUuid = data.uuid;
  });

  it('submits an application with valid application data', async () => {
    const applicationData = generateApplicationData();
    const response = await api.post(`/app/${appUuid}/submit`, {
      applicationFields: applicationData,
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_SUBMITTED');
    expect(data.data).toHaveProperty('pandadoc');
  }, 30000);

  it('rejects invalid application data format', async () => {
    const invalidData = {
      applicationFields: {
        // Missing required fields
        businessName: 'Test Business',
      },
    };

    const response = await api.post(`/app/${appUuid}/submit`, invalidData);
    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Validation Error');
  });
});

// Application Edit Tests
describe('Application Edit', () => {
  let appUuid;

  beforeAll(async () => {
    // Create and submit a test application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    appUuid = createData.uuid;

    // Submit the application
    const applicationData = generateApplicationData();
    await api.post(`/app/${appUuid}/submit`, { applicationFields: applicationData });
  });

  it('edits a submitted application', async () => {
    const response = await api.post(`/app/${appUuid}/edit`, {});

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_EDITING');

    // ensure applicationFields doesn't contain PII
    expect(data.data.applicationFields).not.toHaveProperty('ein');
    expect(data.data.applicationFields.owners[0]).not.toHaveProperty('dateOfBirth');
    expect(data.data.applicationFields.owners[0]).not.toHaveProperty('ssn');
  });

  it('resubmits an edited application', async () => {
    // Then resubmit with updated data
    const updatedData = generateApplicationData({
      businessName: 'Updated Business Name LLC',
      owners: [
        {
          ...generateApplicationData().owners[0],
          firstName: 'First Name',
          ownershipPercentage: 50,
        },
        {
          ...generateApplicationData().owners[0],
          firstName: 'Second Owner',
          ownershipPercentage: 50,
        },
      ],
    });

    const response = await api.post(`/app/${appUuid}/submit`, {
      applicationFields: updatedData,
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_SUBMITTED');
    expect(data.data).toHaveProperty('pandadoc');
  });
});

// PandaDoc Status Tests
describe('PandaDoc Status', () => {
  let appUuid;

  beforeAll(async () => {
    // Create and submit a test application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    appUuid = createData.uuid;

    // Submit the application
    const applicationData = generateApplicationData();
    await api.post(`/app/${appUuid}/submit`, { applicationFields: applicationData });
  }, 30000);

  it('retrieves PandaDoc status for a submitted application', async () => {
    const response = await api.get(`/app/${appUuid}/pandadoc/status`);

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('signed');
    expect(typeof data.signed).toBe('boolean');
  });

  it('returns 404 for application without PandaDoc document', async () => {
    // Create a new application but don't submit it
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    const newAppUuid = createData.uuid;

    response = await api.get(`/app/${newAppUuid}/pandadoc/status`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('PandaDoc document not found');
  });

  it('returns 404 for non-existent application', async () => {
    const nonExistentUuid = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
    const response = await api.get(`/app/${nonExistentUuid}/pandadoc/status`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('not found');
  });
});

// Application Start Tests
describe('Application Start', () => {
  let appUuid;

  beforeAll(async () => {
    // Create a test application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    appUuid = createData.uuid;
  });

  it('starts a PREQUAL_APPROVED application', async () => {
    const response = await api.post(`/app/${appUuid}/start`, {});

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_STARTED');
    expect(data.data).toHaveProperty('preQualifyFields');
  });

  it('rejects starting an application that is not in PREQUAL_APPROVED status', async () => {
    // Create a denied application
    const requestData = generateCreateAppRequest({ preQualifyFields: generateDeniedPrequalData() });
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    const deniedAppUuid = createData.uuid;

    // Try to start it
    response = await api.post(`/app/${deniedAppUuid}/start`, {});

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be started");
  });
});

// Application Signing Tests
describe('Application Signing', () => {
  let appUuid;

  beforeAll(async () => {
    // Create and submit a test application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    appUuid = createData.uuid;

    // Submit the application
    const applicationData = generateApplicationData();
    await api.post(`/app/${appUuid}/submit`, { applicationFields: applicationData });
  });

  it('signs a submitted application', async () => {
    const response = await api.post(`/app/${appUuid}/sign`, {});

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_SIGNED');
  });

  it('rejects signing an application that is not in APP_SUBMITTED status', async () => {
    // Create a new application but don't submit it
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    const newAppUuid = createData.uuid;

    // Try to sign it without submitting
    response = await api.post(`/app/${newAppUuid}/sign`, {});

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be signed");
  });
});

// Application Completion Tests
describe('Application Completion', () => {
  let appUuid;

  beforeAll(async () => {
    // Create, submit, and sign a test application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    appUuid = createData.uuid;

    // Submit the application
    const applicationData = generateApplicationData();
    await api.post(`/app/${appUuid}/submit`, { applicationFields: applicationData });

    // Sign the application
    await api.post(`/app/${appUuid}/sign`, {});
  });

  it('completes a signed application', async () => {
    const response = await api.post(`/app/${appUuid}/complete`, {
      bankStatements: [],
    });

    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toHaveProperty('data');
    expect(data.data).toHaveProperty('uuid', appUuid);
    expect(data.data).toHaveProperty('status', 'APP_COMPLETED');
  });

  it('rejects completing an application that is not in APP_SIGNED status', async () => {
    // Create a new application but don't sign it
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    const createData = await response.json();
    const newAppUuid = createData.uuid;

    response = await api.post(`/app/${newAppUuid}/complete`, {
      bankStatements: [],
    });

    expect(response.status).toBe(400);

    const data = await response.json();
    expect(data).toHaveProperty('error');
    expect(data.error).toContain("Application can't be completed");
  });
});

// Complete Application Flow Test
describe('Complete Application Flow', () => {
  it('processes an application through the entire lifecycle', async () => {
    // 1. Create application
    const requestData = generateCreateAppRequest();
    let response = await api.post('/app', requestData);
    expect(response.status).toBe(201);

    let data = await response.json();
    const appUuid = data.uuid;
    expect(data.status).toBe('PREQUAL_APPROVED');

    // 2. Start application
    response = await api.post(`/app/${appUuid}/start`, {});
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data.status).toBe('APP_STARTED');

    // 3. Submit application
    const applicationData = generateApplicationData();
    response = await api.post(`/app/${appUuid}/submit`, {
      applicationFields: applicationData,
    });
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data.status).toBe('APP_SUBMITTED');

    // 4. Sign application
    response = await api.post(`/app/${appUuid}/sign`, {});
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data.status).toBe('APP_SIGNED');

    // 5. Complete application
    const bankStatements = [
      {
        name: 'test.pdf',
        type: 'application/pdf',
        size: 12345,
        dataUrl: 'data:application/pdf;base64,SGVsbG8=',
        preview: 'blob:https://dev.pinnacle-application-portal.pages.dev/24a6e9a5-c27c-4d2a-86db-5d3a8e9ff381',
        lastModified: *************,
      },
      {
        name: 'test2.pdf',
        type: 'application/pdf',
        size: 12345,
        dataUrl: 'data:application/pdf;base64,SGVsbG8=',
        preview: 'blob:https://dev.pinnacle-application-portal.pages.dev/24a6e9a5-c27c-4d2a-86db-5d3a8e9ff381',
        lastModified: *************,
      },
      {
        name: 'test3.pdf',
        type: 'application/pdf',
        size: 12345,
        dataUrl: 'data:application/pdf;base64,SGVsbG8=',
        preview: 'blob:https://dev.pinnacle-application-portal.pages.dev/24a6e9a5-c27c-4d2a-86db-5d3a8e9ff381',
        lastModified: *************,
      },
    ];
    response = await api.post(`/app/${appUuid}/complete`, {
      bankStatements,
    });
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data.status).toBe('APP_COMPLETED');

    // 6. Verify final state
    response = await api.get(`/app/${appUuid}`);
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data.status).toBe('APP_COMPLETED');
    expect(data.data).toHaveProperty('created_at');
  });
});
