-- Migration number: 0001 	 2025-07-14T09:43:13.782Z
-- Applications table for storing application data

CREATE TABLE IF NOT EXISTS applications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  `version` INTEGER NOT NULL,
  `status` TEXT NOT NULL,
  domain TEXT NOT NULL,
  preQualifyFields TEXT NOT NULL, -- <PERSON><PERSON><PERSON> stored as TEXT
  approvalAmount INTEGER DEFAULT 0 CHECK (approvalAmount >= 0),
  agent TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT
  reason TEXT, -- denial reason if denied
  applicationFields TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT
  pandadoc TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT
  utm TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT
  meta TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT
  fastTrack INTEGER NOT NULL DEFAULT 0 CHECK (fastTrack IN (0, 1)), -- BOOLEAN as INTEGER (0/1)
  salesforce_id VARCHAR(18) UNIQUE CHECK (length(salesforce_id) = 18), -- Salesforce record ID of Deal__c (18 chars)
  bank_stmts INTEGER CHECK (bank_stmts >= 0), -- how many bank statements were uploaded, 0 means it was skipped
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  started_at TEXT,
  submitted_at TEXT,
  signed_at TEXT,
  completed_at TEXT,
  edited_at TEXT
);

-- Meta table for key-value storage
-- Used for configuration data like round-robin state, agents list, etc.

CREATE TABLE IF NOT EXISTS meta (
  `key` TEXT PRIMARY KEY,
  `value` TEXT NOT NULL,
  `created_at` TEXT NOT NULL DEFAULT (datetime('now')),
  `updated_at` TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Insert default agents/round-robin data
INSERT OR IGNORE INTO
  meta (`key`, `value`)
VALUES
  (
    'agents:list',
    '[{"id":"005fL000003Q7lhQAC","name":"Pre-Assigned Agent #1","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/35.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003Q7lhQAC","name":"Pre-Assigned Agent #2","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/20.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003Q7lhQAC","name":"Pre-Assigned Agent #3","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/generic.png","calendlyUrl":"https://calendly.com"}]'
  ),
  ('round-robin:index', '0'),
  (
    'round-robin:list',
    '[{"id":"005fL000003Q7lhQAC","name":"Round Robin #1","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/35.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003Q7lhQAC","name":"Round Robin #2","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/20.png","calendlyUrl":"https://calendly.com"},{"id":"005fL000003Q7lhQAC","name":"Round Robin #3","phone":"(*************","email":"<EMAIL>","image":"https://static.pinnaclefunding.com/agent-images/dev/generic.png","calendlyUrl":"https://calendly.com"}]'
  );
